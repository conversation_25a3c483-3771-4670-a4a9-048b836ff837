import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Showcase from "@/components/blocks/showcase";
import ImageCropper from "@/components/image-cropper";
import { getLandingPage } from "@/services/page";
import { setRequestLocale, getTranslations } from "next-intl/server";
import { ImageCropperTexts } from "@/types/image-cropper";

export const revalidate = 60;
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const page = await getLandingPage(locale);
  const t = await getTranslations();

  // 构建图片裁剪组件的文案
  const imageCropperTexts: ImageCropperTexts = {
    cropSettings: t("image_cropper.crop_settings"),
    selectImage: t("image_cropper.select_image"),
    downloadImage: t("image_cropper.download_image"),
    reset: t("image_cropper.reset"),
    width: t("image_cropper.width"),
    height: t("image_cropper.height"),
    xCoordinate: t("image_cropper.x_coordinate"),
    yCoordinate: t("image_cropper.y_coordinate"),
    zoomControl: t("image_cropper.zoom_control"),
    currentZoom: t("image_cropper.current_zoom"),
    commonSizes: t("image_cropper.common_sizes"),
    clickToSelectImage: t("image_cropper.click_to_select_image"),
    supportedFormats: t("image_cropper.supported_formats"),
    scrollToZoom: t("image_cropper.scroll_to_zoom"),
    dragToUpload: t("image_cropper.drag_to_upload"),
    dropToUpload: t("image_cropper.drop_to_upload"),
    zoomOut: t("image_cropper.zoom_out"),
    zoomIn: t("image_cropper.zoom_in"),
    oneToOne: t("image_cropper.one_to_one"),
    fitToWindow: t("image_cropper.fit_to_window"),
  };

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}

      {/* 图片裁剪功能 */}
      <ImageCropper texts={imageCropperTexts} />

      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
